import { ExperienceFiltersType } from "@/app/(site)/experience/schema";
import { defineQuery } from "next-sanity";

const fullExperienceFields = `
  _id,
  _type,
  _createdAt,
  _updatedAt,
  "name": coalesce(
    name[_key == $lang][0].value,
    name[_key == "pt"][0].value,
    "Translation missing"
  ),
  "slug": slug.current,
  "description": coalesce(
    description[_key == $lang][0].value,
    description[_key == "pt"][0].value,
    "Translation missing"
  ),
  "provider": provider-> {
    _id,
    "name": coalesce(
      name[_key == $lang][0].value,
      name[_key == "pt"][0].value,
      "Translation missing"
    ),
    "description": coalesce(
      description[_key == $lang][0].value,
      description[_key == "pt"][0].value,
      "Translation missing"
    ),
    "slug": slug.current,
    contacts[] {
      type,
      value
    }
  },
  medias[] {
    asset-> {
      url
    }
  },
  type,
  locations[] {
    "name": coalesce(
      name[_key == $lang][0].value,
      name[_key == "pt"][0].value,
      "Translation missing"
    ),
    province,
    mapLink
  },
  features[] {
    "name": coalesce(
      name[_key == $lang][0].value,
      name[_key == "pt"][0].value,
      "Translation missing"
    ),
    "description": coalesce(
      description[_key == $lang][0].value,
      description[_key == "pt"][0].value,
      "Translation missing"
    )
  },
  details[] {
    "title": coalesce(
      title[_key == $lang][0].value,
      title[_key == "pt"][0].value,
      "Translation missing"
    ),
    "description": coalesce(
      description[_key == $lang][0].value,
      description[_key == "pt"][0].value,
      "Translation missing"
    )
  },
  prices[] {
    type,
    price,
    "description": coalesce(
      description[_key == $lang][0].value,
      description[_key == "pt"][0].value,
      "Translation missing"
    )
  },
  checkoutMethods[] {
    "name": coalesce(
      name[_key == $lang][0].value,
      name[_key == "pt"][0].value,
      "Translation missing"
    ),
    type,
    value
  },
  highlightedUntil,
  sponsoredUntil,
  duration,
  availability[] {
    startAt,
    endAt,
    "label": coalesce(
      label[_key == $lang][0].value,
      label[_key == "pt"][0].value,
      "Translation missing"
    ),
    status
  },
  items[] {
    "name": coalesce(
      name[_key == $lang][0].value,
      name[_key == "pt"][0].value,
      "Translation missing"
    ),
    "description": coalesce(
      description[_key == $lang][0].value,
      description[_key == "pt"][0].value,
      "Translation missing"
    )
  }
`;

const experienceCardFields = `
  _id,
  _type,
  _createdAt,
  _updatedAt,
  "name": coalesce(
    name[_key == $lang][0].value,
    name[_key == "pt"][0].value,
    "Translation missing"
  ),
  "description": coalesce(
    description[_key == $lang][0].value,
    description[_key == "pt"][0].value,
    "Translation missing"
  ),
  medias[] {
    asset-> {
      url
    }
  },
  type,
  locations[] {
    "name": coalesce(
      name[_key == $lang][0].value,
      name[_key == "pt"][0].value,
      "Translation missing"
    ),
    province,
    mapLink
  },
  prices[] {
    type,
    price,
  },
  highlightedUntil,
  sponsoredUntil,
  duration,
  availability[] {
    startAt,
    endAt,
    "label": coalesce(
      label[_key == $lang][0].value,
      label[_key == "pt"][0].value,
      "Translation missing"
    ),
    status
  }
`;

const orderBy = (sortBy?: string) => {
  switch (sortBy) {
    case "name":
      return "| order(name[0].value asc)";
    case "createdAt":
      return "| order(_createdAt desc)";
    case "updatedAt":
      return "| order(_updatedAt desc)";
    case "type":
      return "| order(type asc)";
    default:
      return "| order(_createdAt desc)";
  }
};

export const getAllExperiencesQuery = (filters?: ExperienceFiltersType) => {
  const conditions = [
    '_type == "experience"',
    !filters?.highlightedUntil &&
      !filters?.sponsoredUntil &&
      "dateTime(now()) <= dateTime(now())",
    filters?.highlightedUntil &&
      "dateTime(highlightedUntil) >= dateTime(now())",
    filters?.sponsoredUntil && "dateTime(sponsoredUntil) >= dateTime(now())",
    filters?.searchKey &&
      `coalesce(name[_key == $lang][0].value, name[_key == "pt"][0].value) match "${filters.searchKey}*"`,
    filters?.type && `type == "${filters.type}"`,
    filters?.province && `"${filters.province}" in locations[].province`,
    filters?.providerId && `provider._ref == "${filters.providerId}"`,
    filters?.minPrice && `prices[].price >= ${filters.minPrice}`,
    filters?.maxPrice && `prices[].price <= ${filters.maxPrice}`,
  ].filter(Boolean);

  const whereClause = conditions.length > 0 ? conditions.join(" && ") : "true";
  const limitClause = filters?.limit ? `[0...${filters.limit}]` : "";

  return defineQuery(
    `*[${whereClause}] ${orderBy(filters?.sortBy)} {${experienceCardFields}} ${limitClause}`,
  );
};

export const getExperienceBySlugQuery = defineQuery(`
  *[_type == "experience" && slug.current == $slug][0] {
    ${fullExperienceFields}
  }
`);

export const getExperienceByIdQuery = defineQuery(`
  *[_type == "experience" && _id == $id][0] {
    ${fullExperienceFields}
  }
`);

export const getExperiencesByIdsQuery = defineQuery(`
  *[_type == "experience" && _id in $ids] {
    ${experienceCardFields}
  }
`);

export const getExperienceLocationsQuery = defineQuery(`
  *[_type == "experience" && defined(locations)] {
    locations[] {
      "name": coalesce(
        name[_key == $lang][0].value,
        name[_key == "pt"][0].value,
        "Translation missing"
      ),
      province
    }
  } | order(locations[0].name asc)
`);

export const getExperienceProvidersQuery = defineQuery(`
  *[_type == "provider" && defined(slug.current)] {
    _id,
    "name": coalesce(
      name[_key == $lang][0].value,
      name[_key == "pt"][0].value,
      "Translation missing"
    ),
    "slug": slug.current
  } | order(name asc)
`);

// Provider queries
export const getProviderByIdQuery = defineQuery(`
  *[_type == "provider" && _id == $id][0] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    "name": coalesce(
      name[_key == $lang][0].value,
      name[_key == "pt"][0].value,
      "Translation missing"
    ),
    "slug": slug.current,
    "description": coalesce(
      description[_key == $lang][0].value,
      description[_key == "pt"][0].value,
      "Translation missing"
    ),
    contacts[] {
      type,
      value
    }
  }
`);

export const getProviderBySlugQuery = defineQuery(`
  *[_type == "provider" && slug.current == $slug][0] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    "name": coalesce(
      name[_key == $lang][0].value,
      name[_key == "pt"][0].value,
      "Translation missing"
    ),
    "slug": slug.current,
    "description": coalesce(
      description[_key == $lang][0].value,
      description[_key == "pt"][0].value,
      "Translation missing"
    ),
    contacts[] {
      type,
      value
    }
  }
`);

// ExperienceFavorite queries
export const getUserExperienceFavoritesQuery = defineQuery(`
  *[_type == "experienceFavorite" && userId == $userId] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    userId,
    experienceId,
    "experience": experience-> {
      _id,
      "name": coalesce(
        name[_key == $lang][0].value,
        name[_key == "pt"][0].value,
        "Translation missing"
      )
    }
  } | order(_createdAt desc)
`);

export const getExperienceFavoriteQuery = defineQuery(`
  *[_type == "experienceFavorite" && userId == $userId && experienceId == $experienceId][0] {
    _id,
    _type,
    _createdAt,
    _updatedAt,
    userId,
    experienceId,
    "experience": experience-> {
      _id,
      "name": coalesce(
        name[_key == $lang][0].value,
        name[_key == "pt"][0].value,
        "Translation missing"
      )
    }
  }
`);
