import { beforeAll, afterAll } from "vitest";
import dotenv from "dotenv";

dotenv.config({ path: ".env" });

beforeAll(async () => {
  if (!process.env.INTERNAL_API_KEY) {
    throw new Error(
      "INTERNAL_API_KEY environment variable is required for testing",
    );
  }

  if (!process.env.NEXT_PUBLIC_SANITY_PROJECT_ID) {
    throw new Error(
      "NEXT_PUBLIC_SANITY_PROJECT_ID environment variable is required for testing",
    );
  }

  if (!process.env.NEXT_PUBLIC_SANITY_DATASET) {
    throw new Error(
      "NEXT_PUBLIC_SANITY_DATASET environment variable is required for testing",
    );
  }
});

afterAll(async () => {
  // Global cleanup if needed
});
