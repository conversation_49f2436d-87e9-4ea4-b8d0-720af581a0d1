import type { StructureResolver } from "sanity/structure";

// https://www.sanity.io/docs/structure-builder-cheat-sheet
export const structure: StructureResolver = (S) =>
  S.list()
    .title("Zimbora")
    .items([
      S.documentTypeListItem("event"),
      S.documentTypeListItem("experience"),
      S.documentTypeListItem("provider"),
      S.documentTypeListItem("category"),
      S.documentTypeListItem("notification"),
      S.divider(),
      ...S.documentTypeListItems().filter(
        (item) =>
          item.getId() &&
          ![
            "event",
            "category",
            "author",
            "notification",
            "experience",
            "provider",
          ].includes(item.getId()!),
      ),
    ]);
