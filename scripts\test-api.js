#!/usr/bin/env node

/**
 * API Test Runner Script
 * 
 * This script helps run API integration tests by:
 * 1. Checking if the development server is running
 * 2. Providing helpful instructions if not
 * 3. Running the tests if the server is available
 */

const { spawn } = require('child_process');
const http = require('http');

const SERVER_URL = 'http://localhost:3000';
const CHECK_TIMEOUT = 5000; // 5 seconds

async function checkServerRunning() {
  return new Promise((resolve) => {
    const req = http.get(SERVER_URL, (res) => {
      resolve(true);
    });

    req.on('error', () => {
      resolve(false);
    });

    req.setTimeout(CHECK_TIMEOUT, () => {
      req.destroy();
      resolve(false);
    });
  });
}

async function runTests() {
  console.log('🔍 Checking if development server is running...');
  
  const serverRunning = await checkServerRunning();
  
  if (!serverRunning) {
    console.log('\n❌ Development server is not running on http://localhost:3000');
    console.log('\n📋 To run API integration tests:');
    console.log('   1. Start the development server: pnpm dev');
    console.log('   2. Wait for "Ready" message');
    console.log('   3. Run this script again: node scripts/test-api.js');
    console.log('   4. Or run tests directly: pnpm test:api');
    console.log('\n💡 These are integration tests that make real HTTP requests to your API endpoints.');
    process.exit(1);
  }

  console.log('✅ Development server is running');
  console.log('🧪 Running API integration tests...\n');

  // Run the tests
  const testProcess = spawn('pnpm', ['test:api'], {
    stdio: 'inherit',
    shell: true
  });

  testProcess.on('close', (code) => {
    if (code === 0) {
      console.log('\n✅ All tests passed!');
    } else {
      console.log('\n❌ Some tests failed. Check the output above for details.');
    }
    process.exit(code);
  });

  testProcess.on('error', (error) => {
    console.error('Error running tests:', error);
    process.exit(1);
  });
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Test runner interrupted');
  process.exit(0);
});

runTests().catch((error) => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
