import { client } from "@/sanity/lib/client";
import {
  getAllExperiencesQuery,
  getExperienceBySlugQuery,
  getExperienceByIdQuery,
  getExperiencesByIdsQuery,
  getExperienceLocationsQuery,
  getExperienceProvidersQuery,
  getProviderByIdQuery,
  getProviderBySlugQuery,
  getUserExperienceFavoritesQuery,
  getExperienceFavoriteQuery,
} from "@/sanity/queries/experience";
import { z } from "zod";
import {
  experienceSchema,
  providerSchema,
  experienceFavoriteSchema,
  ExperienceFiltersType,
  experienceFiltersSchema,
} from "../schema";
import { LocaleType, DEFAULT_LANGUAGE } from "../../api/i18n";

// Experience controllers
export const getAllExperiences = async (
  filters?: ExperienceFiltersType,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  let validatedFilters: ExperienceFiltersType = {};

  if (filters) {
    const parsedFilters = experienceFiltersSchema.safeParse(filters);
    if (parsedFilters.success) {
      validatedFilters = parsedFilters.data;
    } else {
      console.error("Invalid filters:", parsedFilters.error);
      return null;
    }
  }

  const experiences = await client
    .fetch(getAllExperiencesQuery(validatedFilters), { lang })
    .catch((err) => {
      console.error("Error fetching experiences:", err);
      return null;
    });

  if (!experiences) {
    return null;
  }

  // Remove duration field from experiences if it exists (backward compatibility)
  const cleanedExperiences = experiences.map((exp: any) => {
    const { duration, ...rest } = exp;
    return rest;
  });

  const schema = z.array(experienceSchema).safeParse(cleanedExperiences);

  if (!schema.success) {
    console.error("Experience validation error:", schema.error);
    return null;
  }

  return schema.data;
};

export const getExperienceBySlug = async (
  slug: string,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  const experience = await client
    .fetch(getExperienceBySlugQuery, { slug, lang })
    .catch((err) => {
      console.error("Error fetching experience by slug:", err);
      return null;
    });

  if (!experience) {
    return null;
  }

  // Remove duration field from experience if it exists (backward compatibility)
  const { duration, ...cleanedExperience } = experience;

  const schema = experienceSchema.safeParse(cleanedExperience);

  if (!schema.success) {
    console.error("Experience validation error:", schema.error);
    return null;
  }

  return schema.data;
};

export const getExperienceById = async (
  id: string,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  const experience = await client
    .fetch(getExperienceByIdQuery, { id, lang })
    .catch((err) => {
      console.error("Error fetching experience by id:", err);
      return null;
    });

  if (!experience) {
    return null;
  }

  // Remove duration field from experience if it exists (backward compatibility)
  const { duration, ...cleanedExperience } = experience;

  const schema = experienceSchema.safeParse(cleanedExperience);

  if (!schema.success) {
    console.error("Experience validation error:", schema.error);
    return null;
  }

  return schema.data;
};

export const getExperiencesByIds = async (
  ids: string[],
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  const experiences = await client
    .fetch(getExperiencesByIdsQuery, { ids, lang })
    .catch((err) => {
      console.error("Error fetching experiences by ids:", err);
      return null;
    });

  if (!experiences) {
    return null;
  }

  // Remove duration field from experiences if it exists (backward compatibility)
  const cleanedExperiences = experiences.map((exp: any) => {
    const { duration, ...rest } = exp;
    return rest;
  });

  const schema = z.array(experienceSchema).safeParse(cleanedExperiences);

  if (!schema.success) {
    console.error("Experiences validation error:", schema.error);
    return null;
  }

  return schema.data;
};

type LocationType = {
  name: string;
  province: string;
};

export const getExperienceLocations = async (
  lang: LocaleType = DEFAULT_LANGUAGE,
): Promise<LocationType[] | null> => {
  const locations = await client
    .fetch(getExperienceLocationsQuery, { lang })
    .catch((err) => {
      console.error("Error fetching experience locations:", err);
      return null;
    });

  if (!locations) {
    return null;
  }

  // Flatten and deduplicate locations
  const flatLocations = locations.flatMap((item: any) => item.locations || []);
  const uniqueLocations = flatLocations.filter(
    (location: any, index: number, self: any[]) =>
      index ===
      self.findIndex(
        (l) => l.name === location.name && l.province === location.province,
      ),
  );

  return z
    .array(
      z.object({
        name: z.string(),
        province: z.string(),
      }),
    )
    .parse(uniqueLocations);
};

type ProviderType = {
  _id: string;
  name: string;
  slug: string;
};

export const getExperienceProviders = async (
  lang: LocaleType = DEFAULT_LANGUAGE,
): Promise<ProviderType[] | null> => {
  const providers = await client
    .fetch(getExperienceProvidersQuery, { lang })
    .catch((err) => {
      console.error("Error fetching experience providers:", err);
      return null;
    });

  if (!providers) {
    return null;
  }

  return z
    .array(
      z.object({
        _id: z.string(),
        name: z.string(),
        slug: z.string(),
      }),
    )
    .parse(providers);
};

// Provider controllers
export const getProviderById = async (
  id: string,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  const provider = await client
    .fetch(getProviderByIdQuery, { id, lang })
    .catch((err) => {
      console.error("Error fetching provider by id:", err);
      return null;
    });

  if (!provider) {
    return null;
  }

  const schema = providerSchema.safeParse(provider);

  if (!schema.success) {
    console.error("Provider validation error:", schema.error);
    return null;
  }

  return schema.data;
};

export const getProviderBySlug = async (
  slug: string,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  const provider = await client
    .fetch(getProviderBySlugQuery, { slug, lang })
    .catch((err) => {
      console.error("Error fetching provider by slug:", err);
      return null;
    });

  if (!provider) {
    return null;
  }

  const schema = providerSchema.safeParse(provider);

  if (!schema.success) {
    console.error("Provider validation error:", schema.error);
    return null;
  }

  return schema.data;
};

// ExperienceFavorite controllers
export const getUserExperienceFavorites = async (
  userId: string,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  const favorites = await client
    .fetch(getUserExperienceFavoritesQuery, { userId, lang })
    .catch((err) => {
      console.error("Error fetching user experience favorites:", err);
      return null;
    });

  if (!favorites) {
    return null;
  }

  const schema = z.array(experienceFavoriteSchema).safeParse(favorites);

  if (!schema.success) {
    console.error("Experience favorites validation error:", schema.error);
    return null;
  }

  return schema.data;
};

export const getExperienceFavorite = async (
  userId: string,
  experienceId: string,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  const favorite = await client
    .fetch(getExperienceFavoriteQuery, { userId, experienceId, lang })
    .catch((err) => {
      console.error("Error fetching experience favorite:", err);
      return null;
    });

  if (!favorite) {
    return null;
  }

  const schema = experienceFavoriteSchema.safeParse(favorite);

  if (!schema.success) {
    console.error("Experience favorite validation error:", schema.error);
    return null;
  }

  return schema.data;
};
