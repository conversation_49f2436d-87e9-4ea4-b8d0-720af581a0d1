# API Integration Tests

This directory contains integration tests for the Zimbora Web API endpoints using Vitest.

## Setup

The tests are configured to run against the actual API endpoints using HTTP requests. This allows us to test the complete request/response cycle including:

- Authentication (API key validation)
- Request parsing
- Business logic
- Response formatting
- Schema validation

## Configuration

### Environment Variables

Make sure you have the following environment variables set in your `.env` file:

```env
INTERNAL_API_KEY=your-api-key-here
NEXT_PUBLIC_SANITY_PROJECT_ID=your-sanity-project-id
NEXT_PUBLIC_SANITY_DATASET=your-sanity-dataset
```

### Vitest Configuration

The tests are configured in `vitest.config.ts` with:

- Node environment for server-side testing
- Global test utilities
- Path aliases matching the main project
- Setup files for environment configuration

## Running Tests

**Important**: These are integration tests that require the development server to be running.

### Step 1: Start the Development Server

```bash
# In one terminal, start the development server
pnpm dev
```

Wait for the server to start and show "Ready" message.

### Step 2: Run the Tests

```bash
# In another terminal, run the tests
pnpm test:api

# Or run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run specific test file
pnpm vitest tests/api/experiences.test.ts
```

### Expected Behavior

- ✅ **Server Running**: Tests should pass and validate API responses
- ❌ **Server Not Running**: Tests will fail with `ECONNREFUSED` errors (this is expected)

### Test Results

When the server is running, you should see:

- Schema validation passing
- Authentication tests working
- Response structure validation
- Error handling verification

## Test Structure

### API Client (`tests/utils/api-client.ts`)

A utility class for making HTTP requests to the API with:

- Automatic API key injection
- Language header support
- Request/response typing
- Error handling

### Test Files

- `tests/api/experiences.test.ts` - Tests for the experiences API endpoints

## What We Test

### Schema Validation

- Response structure matches expected Zod schemas
- Required fields are present
- Data types are correct

### Authentication

- Valid API key allows access
- Missing API key returns 401
- Invalid API key returns 401

### Functionality

- Endpoints return expected data
- Filtering parameters work correctly
- Language support works
- Error handling is appropriate

### Integration

- Full request/response cycle
- Database queries work correctly
- Business logic executes properly

## Adding New Tests

1. Create a new test file in `tests/api/`
2. Import the API client and relevant schemas
3. Write tests following the existing patterns
4. Ensure proper cleanup and error handling

Example:

```typescript
import { describe, it, expect } from "vitest";
import { apiClient } from "../utils/api-client";
import { yourSchema } from "@/path/to/schema";

describe("Your API Tests", () => {
  it("should do something", async () => {
    const response = await apiClient.get("/api/your-endpoint");
    expect(response.status).toBe(200);
    // Add schema validation and other assertions
  });
});
```

## Notes

- Tests run against the actual development server
- Make sure your development server is running before running tests
- Tests use real data from your Sanity CMS
- Be mindful of rate limits and API quotas
